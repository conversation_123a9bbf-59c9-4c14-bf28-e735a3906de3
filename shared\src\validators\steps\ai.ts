import { RpaStep, ExtractPdfTextStep, ValidationResult, ValidationError, createStepFromType } from '../../types';

export function validateAiStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractPdfText':
      const pdfStep = step as ExtractPdfTextStep;
      
      if (!pdfStep.base64Input || pdfStep.base64Input.trim() === '') {
        errors.push({
          field: 'base64Input',
          message: 'Base64 input is required for PDF text extraction',
          code: 'REQUIRED'
        });
      }

      if (!pdfStep.prompt || pdfStep.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for PDF text extraction',
          code: 'REQUIRED'
        });
      }

      // variableName is optional (defaults to 'var-ai-response')
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createAiStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractPdfText':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an AI step type: ${stepType}`);
  }
}

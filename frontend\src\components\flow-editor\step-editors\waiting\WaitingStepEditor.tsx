import { useState } from 'react'
import { validateStep, WaitForSelectorStep, WaitForTimeoutStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { SelectorField, SelectField, <PERSON>Field } from '../base/FieldComponents'

type WaitingStep = WaitForSelectorStep | WaitForTimeoutStep

interface WaitingStepEditorProps extends BaseStepEditorProps {
  step: WaitingStep
}

export function WaitingStepEditor({
  step,
  onSave,
  onCancel,
  compact = false
}: WaitingStepEditorProps) {
  const [editedStep, setEditedStep] = useState<WaitingStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<WaitingStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as WaitingStep))
  }

  const renderWaitingFields = () => {
    switch (editedStep.type) {
      case 'waitForSelector':
        const waitSelectorStep = editedStep as WaitForSelectorStep
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={waitSelectorStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="Element selector to wait for"
              compact={compact}
            />
            <SelectField
              label="Tillstånd"
              value={waitSelectorStep.state || 'visible'}
              onChange={(value) => updateStep({ state: value as any })}
              options={[
                { value: 'visible', label: 'Synlig' },
                { value: 'hidden', label: 'Dold' },
                { value: 'attached', label: 'Bifogad' },
                { value: 'detached', label: 'Frånkopplad' }
              ]}
              compact={compact}
            />
          </>
        )

      case 'waitForTimeout':
        const waitTimeoutStep = editedStep as WaitForTimeoutStep
        return (
          <NumberField
            label="Duration (ms)"
            value={waitTimeoutStep.duration || 1000}
            onChange={(value) => updateStep({ duration: value })}
            min={0}
            compact={compact}
          />
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      waitForSelector: 'Wait for Element',
      waitForTimeout: 'Wait for Time'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderWaitingFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}

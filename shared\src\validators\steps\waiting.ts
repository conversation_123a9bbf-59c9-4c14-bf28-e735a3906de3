import { RpaStep, WaitF<PERSON><PERSON>electorStep, WaitForTimeoutStep, Val<PERSON>tionResult, ValidationError, createStepFromType } from '../../types';

export function validateWaitingStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'waitForSelector':
      const selectorStep = step as WaitForSelectorStep;
      if (!selectorStep.selector || selectorStep.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          code: 'REQUIRED'
        });
      }
      break;

    case 'waitForTimeout':
      const timeoutStep = step as WaitForTimeoutStep;
      if (!timeoutStep.duration || timeoutStep.duration <= 0) {
        errors.push({
          field: 'duration',
          message: 'Duration must be greater than 0',
          code: 'INVALID_VALUE'
        });
      }
      break;

    case 'waitForUrl':
      // URL validation could be added here if needed
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createWaitingStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'waitForSelector':
    case 'waitForTimeout':
    case 'waitForUrl':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a waiting step type: ${stepType}`);
  }
}

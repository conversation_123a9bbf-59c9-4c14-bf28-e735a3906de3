import { useState } from 'react'
import { validateStep, ExtractPdfTextStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { VariableField, TextAreaField, TextField } from '../base/FieldComponents'

type AIStep = ExtractPdfTextStep

interface AIStepEditorProps extends BaseStepEditorProps {
  step: AIStep
}

export function AIStepEditor({ 
  step, 
  onSave, 
  onCancel, 
  compact = false, 
  steps = [], 
  currentStepIndex = 0 
}: AIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<AIStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<AIStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as AIStep))
  }

  const renderAIFields = () => {
    switch (editedStep.type) {
      case 'extractPdfText':
        const pdfStep = editedStep as ExtractPdfTextStep
        return (
          <>
            <VariableField
              label="Base64 PDF Data"
              value={pdfStep.base64Input || ''}
              onChange={(value) => updateStep({ base64Input: value })}
              placeholder="Ange base64 data eller välj variabel"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
              fullWidth={true}
            />
            <TextAreaField
              label="AI Prompt"
              value={pdfStep.prompt || ''}
              onChange={(value) => updateStep({ prompt: value })}
              placeholder="Beskriv vad du vill göra med PDF-texten, t.ex. 'Extrahera alla namn och telefonnummer'"
              minHeight="80px"
              compact={compact}
              fullWidth={true}
            />
            <TextField
              label="Variabelnamn för resultat"
              value={pdfStep.variableName || 'var-ai-response'}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="var-ai-response"
              compact={compact}
            />
          </>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      extractPdfText: 'Extract PDF Text'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}

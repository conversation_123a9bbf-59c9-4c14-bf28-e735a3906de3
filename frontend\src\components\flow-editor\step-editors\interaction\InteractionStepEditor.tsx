import { useState } from 'react'
import { RpaStep, validateStep, ClickStep, FillStep, TypeStep, SelectOptionStep, CheckStep, UncheckStep, ConditionalClickStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { SelectorField, VariableField, SelectField, NumberField } from '../base/FieldComponents'

type InteractionStep = ClickStep | FillStep | TypeStep | SelectOptionStep | CheckStep | UncheckStep | ConditionalClickStep

interface InteractionStepEditorProps extends BaseStepEditorProps {
  step: InteractionStep
}

export function InteractionStepEditor({ 
  step, 
  onSave, 
  onCancel, 
  compact = false, 
  steps = [], 
  currentStepIndex = 0 
}: InteractionStepEditorProps) {
  const [editedStep, setEditedStep] = useState<InteractionStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<InteractionStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as InteractionStep))
  }

  const renderInteractionFields = () => {
    switch (editedStep.type) {
      case 'click':
        const clickStep = editedStep as ClickStep
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={clickStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="button, #id, .class"
              compact={compact}
            />
            <SelectField
              label="Musknapp"
              value={clickStep.button || 'left'}
              onChange={(value) => updateStep({ button: value as any })}
              options={[
                { value: 'left', label: 'Vänster' },
                { value: 'right', label: 'Höger' },
                { value: 'middle', label: 'Mitten' }
              ]}
              compact={compact}
            />
          </>
        )

      case 'fill':
        const fillStep = editedStep as FillStep
        return (
          <>
            <VariableField
              label="Element Selector"
              value={fillStep.selector || ''}
              onChange={(value) => updateStep({ selector: value })}
              placeholder="input, textarea, [name='field']"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
              fullWidth={true}
            />
            <VariableField
              label="Value"
              value={fillStep.value || ''}
              onChange={(value) => updateStep({ value: value })}
              placeholder="Text to fill"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
          </>
        )

      case 'type':
        const typeStep = editedStep as TypeStep
        return (
          <>
            <VariableField
              label="Element Selector"
              value={typeStep.selector || ''}
              onChange={(value) => updateStep({ selector: value })}
              placeholder="input, textarea"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
            <VariableField
              label="Text"
              value={typeStep.text || ''}
              onChange={(value) => updateStep({ text: value })}
              placeholder="Text to type"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
            <NumberField
              label="Delay (ms)"
              value={typeStep.delay || 0}
              onChange={(value) => updateStep({ delay: value })}
              min={0}
              compact={compact}
            />
          </>
        )

      case 'selectOption':
        const selectStep = editedStep as SelectOptionStep
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={selectStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="select, #dropdown-id"
              compact={compact}
            />
            <SelectField
              label="Selection Method"
              value={
                selectStep.value ? 'value' :
                selectStep.label ? 'label' :
                selectStep.index !== undefined ? 'index' : 'value'
              }
              onChange={(method) => {
                if (method === 'value') {
                  updateStep({ value: '', label: undefined, index: undefined })
                } else if (method === 'label') {
                  updateStep({ value: undefined, label: '', index: undefined })
                } else if (method === 'index') {
                  updateStep({ value: undefined, label: undefined, index: 0 })
                }
              }}
              options={[
                { value: 'value', label: 'By Value' },
                { value: 'label', label: 'By Label' },
                { value: 'index', label: 'By Index' }
              ]}
              compact={compact}
            />
            {(selectStep.value !== undefined || (!selectStep.label && selectStep.index === undefined)) && (
              <VariableField
                label="Option Value"
                value={selectStep.value || ''}
                onChange={(value) => updateStep({ value })}
                placeholder="option-value"
                steps={steps}
                currentStepIndex={currentStepIndex}
                compact={compact}
              />
            )}
            {selectStep.label !== undefined && (
              <VariableField
                label="Option Label"
                value={selectStep.label || ''}
                onChange={(value) => updateStep({ label: value })}
                placeholder="Option Text"
                steps={steps}
                currentStepIndex={currentStepIndex}
                compact={compact}
              />
            )}
            {selectStep.index !== undefined && (
              <NumberField
                label="Option Index"
                value={selectStep.index || 0}
                onChange={(value) => updateStep({ index: value })}
                min={0}
                placeholder="0"
                compact={compact}
              />
            )}
          </>
        )

      case 'conditionalClick':
        const conditionalStep = editedStep as ConditionalClickStep
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={conditionalStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="button, #id, .class"
              compact={compact}
            />
            <SelectField
              label="Villkor"
              value={conditionalStep.condition || 'exists'}
              onChange={(value) => updateStep({ condition: value as any })}
              options={[
                { value: 'exists', label: 'Element finns' },
                { value: 'enabled', label: 'Element är aktiverat' },
                { value: 'disabled', label: 'Element är inaktiverat' }
              ]}
              compact={compact}
            />
            <SelectField
              label="Åtgärd"
              value={conditionalStep.clickIfTrue ? 'true' : 'false'}
              onChange={(value) => updateStep({ clickIfTrue: value === 'true' })}
              options={[
                { value: 'true', label: 'Klicka om villkor är sant' },
                { value: 'false', label: 'Klicka om villkor är falskt' }
              ]}
              compact={compact}
            />
            <SelectField
              label="Musknapp"
              value={conditionalStep.button || 'left'}
              onChange={(value) => updateStep({ button: value as any })}
              options={[
                { value: 'left', label: 'Vänster' },
                { value: 'right', label: 'Höger' },
                { value: 'middle', label: 'Mitten' }
              ]}
              compact={compact}
            />
          </>
        )

      case 'check':
      case 'uncheck':
        const checkStep = editedStep as CheckStep | UncheckStep
        return (
          <SelectorField
            label="Element Selector"
            value={checkStep.selector || ''}
            onChange={(selector) => updateStep({ selector })}
            placeholder="input[type='checkbox'], #checkbox-id"
            compact={compact}
          />
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      click: 'Click',
      fill: 'Fill',
      type: 'Type',
      selectOption: 'Select Option',
      check: 'Check',
      uncheck: 'Uncheck',
      conditionalClick: 'Conditional Click'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderInteractionFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database configuration
const DB_PATH = process.env.DB_PATH || path.join(process.cwd(), 'data', 'rpa.db');
const DB_DIR = path.dirname(DB_PATH);

// Ensure database directory exists
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true });
}

// Create database connection
const db = new Database(DB_PATH);

// Enable WAL mode for better performance
db.pragma('journal_mode = WAL');
db.pragma('synchronous = NORMAL');
db.pragma('cache_size = 1000');
db.pragma('temp_store = memory');

// Database version management
const CURRENT_DB_VERSION = 2;

function getDatabaseVersion(): number {
  try {
    const result = db.prepare('PRAGMA user_version').get() as { user_version: number };
    return result.user_version;
  } catch (error) {
    return 0;
  }
}

function setDatabaseVersion(version: number): void {
  db.prepare(`PRAGMA user_version = ${version}`).run();
}

// Database migrations
function runMigrations() {
  const currentVersion = getDatabaseVersion();
  console.log(`📊 Current database version: ${currentVersion}`);

  if (currentVersion < 2) {
    console.log('🔄 Running migration to version 2: Updating executions table for large base64 data...');

    // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
    db.exec(`
      -- Create new executions table with BLOB for results
      CREATE TABLE IF NOT EXISTS executions_new (
        id TEXT PRIMARY KEY,
        flow_id TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME NULL,
        results BLOB DEFAULT '{}', -- Changed to BLOB for large data
        error TEXT NULL,
        FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
      );

      -- Copy existing data if executions table exists
      INSERT OR IGNORE INTO executions_new (id, flow_id, status, started_at, completed_at, results, error)
      SELECT id, flow_id, status, started_at, completed_at, results, error
      FROM executions WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='executions');

      -- Drop old table and rename new one
      DROP TABLE IF EXISTS executions;
      ALTER TABLE executions_new RENAME TO executions;
    `);

    // Also update execution_logs table for large data
    db.exec(`
      -- Create new execution_logs table with BLOB for data
      CREATE TABLE IF NOT EXISTS execution_logs_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        execution_id TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        step_id TEXT NULL,
        data BLOB NULL, -- Changed to BLOB for large data
        FOREIGN KEY (execution_id) REFERENCES executions (id) ON DELETE CASCADE
      );

      -- Copy existing data if execution_logs table exists
      INSERT OR IGNORE INTO execution_logs_new (id, execution_id, timestamp, level, message, step_id, data)
      SELECT id, execution_id, timestamp, level, message, step_id, data
      FROM execution_logs WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='execution_logs');

      -- Drop old table and rename new one
      DROP TABLE IF EXISTS execution_logs;
      ALTER TABLE execution_logs_new RENAME TO execution_logs;
    `);

    // Recreate indexes for both tables
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_executions_flow_id ON executions (flow_id);
      CREATE INDEX IF NOT EXISTS idx_executions_status ON executions (status);
      CREATE INDEX IF NOT EXISTS idx_executions_started_at ON executions (started_at);
      CREATE INDEX IF NOT EXISTS idx_execution_logs_execution_id ON execution_logs (execution_id);
      CREATE INDEX IF NOT EXISTS idx_execution_logs_timestamp ON execution_logs (timestamp);
    `);

    console.log('✅ Migration to version 2 completed');
  }

  // Set the current version
  if (currentVersion < CURRENT_DB_VERSION) {
    setDatabaseVersion(CURRENT_DB_VERSION);
    console.log(`✅ Database updated to version ${CURRENT_DB_VERSION}`);
  }
}

// Initialize database schema
function initializeDatabase() {
  console.log(`📁 Initializing SQLite database at: ${DB_PATH}`);

  // Run migrations first
  runMigrations();

  // Create flows table
  db.exec(`
    CREATE TABLE IF NOT EXISTS flows (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      customer_id TEXT NOT NULL,
      steps TEXT NOT NULL, -- JSON string
      variables TEXT DEFAULT '{}', -- JSON string
      settings TEXT DEFAULT '{}', -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
    )
  `);

  // Create executions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS executions (
      id TEXT PRIMARY KEY,
      flow_id TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      completed_at DATETIME NULL,
      results BLOB DEFAULT '{}', -- BLOB for large data (base64 strings)
      error TEXT NULL,
      FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
    )
  `);

  // Create execution_logs table
  db.exec(`
    CREATE TABLE IF NOT EXISTS execution_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      execution_id TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      level TEXT NOT NULL,
      message TEXT NOT NULL,
      step_id TEXT NULL,
      data BLOB NULL, -- BLOB for large data (base64 strings)
      FOREIGN KEY (execution_id) REFERENCES executions (id) ON DELETE CASCADE
    )
  `);

  // Create schedules table
  db.exec(`
    CREATE TABLE IF NOT EXISTS schedules (
      id TEXT PRIMARY KEY,
      flow_id TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      cron_expression TEXT NOT NULL,
      timezone TEXT DEFAULT 'UTC',
      enabled BOOLEAN DEFAULT 1,
      variables TEXT DEFAULT '{}', -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_run_at DATETIME NULL,
      next_run_at DATETIME NULL,
      FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
    )
  `);

  // Create credentials table
  db.exec(`
    CREATE TABLE IF NOT EXISTS credentials (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      type TEXT NOT NULL CHECK (type IN ('password', '2fa')),
      username TEXT NULL, -- Only for password type
      encrypted_password TEXT NULL, -- Only for password type, encrypted
      encrypted_secret TEXT NULL, -- Only for 2fa type, encrypted
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create customers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      customer_number TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL CHECK (length(name) <= 200),
      visma_number TEXT DEFAULT '',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create customer_tokens table
  db.exec(`
    CREATE TABLE IF NOT EXISTS customer_tokens (
      id TEXT PRIMARY KEY,
      customer_id TEXT NOT NULL,
      name TEXT NOT NULL CHECK (length(name) <= 100),
      description TEXT DEFAULT '',
      encrypted_api_token TEXT NULL, -- Encrypted API token
      encrypted_refresh_token TEXT NULL, -- Encrypted refresh token
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
      UNIQUE(customer_id, name) -- Ensure unique token names per customer
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_executions_flow_id ON executions (flow_id);
    CREATE INDEX IF NOT EXISTS idx_executions_status ON executions (status);
    CREATE INDEX IF NOT EXISTS idx_executions_started_at ON executions (started_at);
    CREATE INDEX IF NOT EXISTS idx_execution_logs_execution_id ON execution_logs (execution_id);
    CREATE INDEX IF NOT EXISTS idx_execution_logs_timestamp ON execution_logs (timestamp);
    CREATE INDEX IF NOT EXISTS idx_schedules_flow_id ON schedules (flow_id);
    CREATE INDEX IF NOT EXISTS idx_schedules_enabled ON schedules (enabled);
    CREATE INDEX IF NOT EXISTS idx_schedules_next_run_at ON schedules (next_run_at);
    CREATE INDEX IF NOT EXISTS idx_credentials_type ON credentials (type);
    CREATE INDEX IF NOT EXISTS idx_credentials_name ON credentials (name);
  `);

  // Create trigger to update updated_at on flows
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_flows_updated_at
    AFTER UPDATE ON flows
    FOR EACH ROW
    BEGIN
      UPDATE flows SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on schedules
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_schedules_updated_at
    AFTER UPDATE ON schedules
    FOR EACH ROW
    BEGIN
      UPDATE schedules SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on credentials
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_credentials_updated_at
    AFTER UPDATE ON credentials
    FOR EACH ROW
    BEGIN
      UPDATE credentials SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on customers
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_customers_updated_at
    AFTER UPDATE ON customers
    FOR EACH ROW
    BEGIN
      UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  console.log('✅ Database schema initialized');
}

// Initialize database on module load
initializeDatabase();

// Prepared statements for better performance
export const statements = {
  // Flow statements
  insertFlow: db.prepare(`
    INSERT INTO flows (id, name, description, customer_id, steps, variables, settings, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateFlow: db.prepare(`
    UPDATE flows
    SET name = ?, description = ?, customer_id = ?, steps = ?, variables = ?, settings = ?, updated_at = ?
    WHERE id = ?
  `),
  
  getFlowById: db.prepare('SELECT * FROM flows WHERE id = ?'),
  getAllFlows: db.prepare('SELECT * FROM flows ORDER BY updated_at DESC'),
  getAllFlowsPaginated: db.prepare('SELECT * FROM flows ORDER BY updated_at DESC LIMIT ? OFFSET ?'),
  deleteFlow: db.prepare('DELETE FROM flows WHERE id = ?'),
  flowExists: db.prepare('SELECT 1 FROM flows WHERE id = ? LIMIT 1'),

  // Execution statements
  insertExecution: db.prepare(`
    INSERT INTO executions (id, flow_id, status, started_at, results)
    VALUES (?, ?, ?, ?, ?)
  `),
  
  updateExecutionStatus: db.prepare(`
    UPDATE executions 
    SET status = ?, completed_at = ?, error = ?
    WHERE id = ?
  `),
  
  updateExecutionResults: db.prepare(`
    UPDATE executions 
    SET results = ?
    WHERE id = ?
  `),
  
  getExecutionById: db.prepare('SELECT * FROM executions WHERE id = ?'),
  getExecutionsByFlowId: db.prepare('SELECT * FROM executions WHERE flow_id = ? ORDER BY started_at DESC'),
  getAllExecutions: db.prepare('SELECT * FROM executions ORDER BY started_at DESC LIMIT ? OFFSET ?'),
  getExecutionsByStatus: db.prepare('SELECT * FROM executions WHERE status = ? ORDER BY started_at DESC LIMIT ? OFFSET ?'),
  deleteExecution: db.prepare('DELETE FROM executions WHERE id = ?'),

  // Execution log statements
  insertExecutionLog: db.prepare(`
    INSERT INTO execution_logs (execution_id, timestamp, level, message, step_id, data)
    VALUES (?, ?, ?, ?, ?, ?)
  `),

  getExecutionLogs: db.prepare(`
    SELECT * FROM execution_logs
    WHERE execution_id = ?
    ORDER BY timestamp ASC
  `),

  deleteExecutionLogs: db.prepare('DELETE FROM execution_logs WHERE execution_id = ?'),

  // Schedule statements
  insertSchedule: db.prepare(`
    INSERT INTO schedules (id, flow_id, name, description, cron_expression, timezone, enabled, variables, created_at, updated_at, next_run_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateSchedule: db.prepare(`
    UPDATE schedules
    SET name = ?, description = ?, cron_expression = ?, timezone = ?, enabled = ?, variables = ?, updated_at = ?, next_run_at = ?
    WHERE id = ?
  `),

  updateScheduleLastRun: db.prepare(`
    UPDATE schedules
    SET last_run_at = ?, next_run_at = ?
    WHERE id = ?
  `),

  getScheduleById: db.prepare('SELECT * FROM schedules WHERE id = ?'),
  getSchedulesByFlowId: db.prepare('SELECT * FROM schedules WHERE flow_id = ? ORDER BY created_at DESC'),
  getAllSchedules: db.prepare('SELECT * FROM schedules ORDER BY created_at DESC'),
  getEnabledSchedules: db.prepare('SELECT * FROM schedules WHERE enabled = 1 ORDER BY next_run_at ASC'),
  getSchedulesDueForExecution: db.prepare('SELECT * FROM schedules WHERE enabled = 1 AND next_run_at <= ? ORDER BY next_run_at ASC'),
  deleteSchedule: db.prepare('DELETE FROM schedules WHERE id = ?'),
  scheduleExists: db.prepare('SELECT 1 FROM schedules WHERE id = ? LIMIT 1'),

  // Credential statements
  insertCredential: db.prepare(`
    INSERT INTO credentials (id, name, description, type, username, encrypted_password, encrypted_secret, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateCredential: db.prepare(`
    UPDATE credentials
    SET name = ?, description = ?, username = ?, encrypted_password = ?, encrypted_secret = ?, updated_at = ?
    WHERE id = ?
  `),

  getCredentialById: db.prepare('SELECT * FROM credentials WHERE id = ?'),
  getAllCredentials: db.prepare('SELECT * FROM credentials ORDER BY name ASC'),
  getCredentialsByType: db.prepare('SELECT * FROM credentials WHERE type = ? ORDER BY name ASC'),
  deleteCredential: db.prepare('DELETE FROM credentials WHERE id = ?'),
  credentialExists: db.prepare('SELECT 1 FROM credentials WHERE id = ? LIMIT 1'),

  // Customer statements
  insertCustomer: db.prepare(`
    INSERT INTO customers (id, customer_number, name, visma_number, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?)
  `),

  updateCustomer: db.prepare(`
    UPDATE customers
    SET customer_number = ?, name = ?, visma_number = ?, updated_at = ?
    WHERE id = ?
  `),

  getCustomerById: db.prepare('SELECT * FROM customers WHERE id = ?'),
  getAllCustomers: db.prepare('SELECT * FROM customers ORDER BY name ASC'),
  getCustomerByNumber: db.prepare('SELECT * FROM customers WHERE customer_number = ?'),
  deleteCustomer: db.prepare('DELETE FROM customers WHERE id = ?'),
  customerExists: db.prepare('SELECT 1 FROM customers WHERE id = ? LIMIT 1'),
  customerNumberExists: db.prepare('SELECT 1 FROM customers WHERE customer_number = ? AND id != ? LIMIT 1'),

  // Customer token statements
  insertCustomerToken: db.prepare(`
    INSERT INTO customer_tokens (id, customer_id, name, description, encrypted_api_token, encrypted_refresh_token, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateCustomerToken: db.prepare(`
    UPDATE customer_tokens
    SET name = ?, description = ?, encrypted_api_token = ?, encrypted_refresh_token = ?, updated_at = ?
    WHERE id = ?
  `),

  getCustomerTokenById: db.prepare('SELECT * FROM customer_tokens WHERE id = ?'),
  getCustomerTokensByCustomerId: db.prepare('SELECT * FROM customer_tokens WHERE customer_id = ? ORDER BY name ASC'),
  deleteCustomerToken: db.prepare('DELETE FROM customer_tokens WHERE id = ?'),
  deleteCustomerTokensByCustomerId: db.prepare('DELETE FROM customer_tokens WHERE customer_id = ?'),
  customerTokenExists: db.prepare('SELECT 1 FROM customer_tokens WHERE id = ? LIMIT 1'),
  customerTokenNameExists: db.prepare('SELECT 1 FROM customer_tokens WHERE customer_id = ? AND name = ? AND id != ? LIMIT 1')
};

// Export database instance for advanced queries
export { db };

// Graceful shutdown
process.on('exit', () => {
  db.close();
});

process.on('SIGINT', () => {
  db.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  db.close();
  process.exit(0);
});

console.log('✅ Database initialized successfully');

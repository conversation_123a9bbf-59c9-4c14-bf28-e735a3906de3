#!/usr/bin/env node

/**
 * Test script to verify the new runner system works correctly
 */

import { FlowExecutor } from './runners/FlowExecutor';
import { getRunnerFactory } from './runners/factory';
import { RpaFlow } from '@rpa-project/shared';

// Test flow with different step types
const testFlow: RpaFlow = {
  id: 'test-flow-001',
  name: 'Test Flow - Runner System',
  description: 'Test flow to verify the new runner system',
  customerId: 'test-customer',
  steps: [
    {
      id: 'step-1',
      type: 'navigate',
      url: 'https://example.com',
      timeout: 30000
    },
    {
      id: 'step-2',
      type: 'waitForTimeout',
      duration: 1000
    },
    {
      id: 'step-3',
      type: 'extractText',
      selector: 'h1',
      variableName: 'pageTitle'
    }
  ],
  settings: {
    headless: true,
    browser: 'chromium'
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

async function testRunnerSystem() {
  console.log('🧪 Testing Runner System...\n');

  try {
    // Test 1: Factory initialization
    console.log('1️⃣ Testing Factory Initialization...');
    const factory = getRunnerFactory();
    const stats = factory.getStats();
    console.log(`   ✅ Registered runners: ${stats.registeredRunners}`);
    console.log(`   ✅ Supported step types: ${stats.supportedStepTypes}`);
    console.log(`   ✅ Active instances: ${stats.activeInstances}\n`);

    // Test 2: Flow analysis
    console.log('2️⃣ Testing Flow Analysis...');
    const analysis = factory.analyzeFlow(testFlow);
    console.log(`   ✅ Required runners: ${analysis.requiredRunners.join(', ')}`);
    console.log(`   ✅ Unsupported steps: ${analysis.unsupportedSteps.length === 0 ? 'None' : analysis.unsupportedSteps.join(', ')}`);
    console.log(`   ✅ Steps by runner:`, analysis.stepsByRunner);
    console.log();

    // Test 3: Step type support
    console.log('3️⃣ Testing Step Type Support...');
    const testSteps = ['navigate', 'click', 'extractText', 'extractPdfText', 'unknownStep'];
    for (const stepType of testSteps) {
      const isSupported = factory.isStepTypeSupported(stepType);
      console.log(`   ${isSupported ? '✅' : '❌'} ${stepType}: ${isSupported ? 'Supported' : 'Not supported'}`);
    }
    console.log();

    // Test 4: Runner creation
    console.log('4️⃣ Testing Runner Creation...');
    try {
      const playwrightRunner = factory.createRunnerForStep('navigate');
      console.log('   ✅ PlaywrightRunner created successfully');
      await playwrightRunner.cleanup();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('   ❌ PlaywrightRunner creation failed:', errorMessage);
    }

    try {
      const aiRunner = factory.createRunnerForStep('extractPdfText');
      console.log('   ✅ AIRunner created successfully');
      await aiRunner.cleanup();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('   ❌ AIRunner creation failed:', errorMessage);
    }
    console.log();

    // Test 5: Flow executor initialization
    console.log('5️⃣ Testing Flow Executor...');
    const executor = new FlowExecutor(
      'test-execution-001',
      (log) => console.log(`   📝 ${log.level.toUpperCase()}: ${log.message}`),
      async () => false // No cancellation
    );

    console.log('   ✅ FlowExecutor created successfully');
    
    // Test flow analysis through executor
    const executorAnalysis = executor.analyzeFlow(testFlow);
    console.log(`   ✅ Executor analysis: ${executorAnalysis.requiredRunners.join(', ')}`);
    console.log();

    // Test 6: Supported step types
    console.log('6️⃣ All Supported Step Types:');
    const supportedTypes = factory.getSupportedStepTypes();
    supportedTypes.forEach((stepType, index) => {
      console.log(`   ${index + 1}. ${stepType}`);
    });
    console.log();

    // Test 7: Cleanup
    console.log('7️⃣ Testing Cleanup...');
    await executor.cleanup();
    console.log('   ✅ Executor cleanup completed');

    const finalStats = factory.getStats();
    console.log(`   ✅ Final active instances: ${finalStats.activeInstances}`);
    console.log();

    console.log('🎉 All tests passed! Runner system is working correctly.\n');

    // Summary
    console.log('📊 Test Summary:');
    console.log(`   • Registered Runners: ${stats.registeredRunners}`);
    console.log(`   • Supported Step Types: ${stats.supportedStepTypes}`);
    console.log(`   • Test Flow Analysis: ${analysis.requiredRunners.length} runner(s) required`);
    console.log(`   • All core functionality verified ✅`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests
testRunnerSystem().catch(console.error);

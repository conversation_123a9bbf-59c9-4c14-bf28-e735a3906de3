import { useState } from 'react'
import { useScrollFade } from '../../hooks/useScrollFade'

interface StepToolbarProps {
  onAddStep: (stepType: string) => void
}

interface StepCategory {
  name: string
  icon: string
  steps: StepDefinition[]
}

interface StepDefinition {
  type: string
  name: string
  icon: string
  description: string
}

const stepCategories: StepCategory[] = [
  {
    name: 'Navigation',
    icon: '🧭',
    steps: [
      {
        type: 'navigate',
        name: 'Navigate',
        icon: '🌐',
        description: 'Navigate to a URL'
      },
      {
        type: 'goBack',
        name: 'Go Back',
        icon: '⬅️',
        description: 'Go back in browser history'
      },
      {
        type: 'goForward',
        name: 'Go Forward',
        icon: '➡️',
        description: 'Go forward in browser history'
      },
      {
        type: 'reload',
        name: 'Reload',
        icon: '🔄',
        description: 'Reload the current page'
      }
    ]
  },
  {
    name: 'Interactions',
    icon: '👆',
    steps: [
      {
        type: 'click',
        name: 'Click',
        icon: '👆',
        description: 'Click on an element'
      },
      {
        type: 'fill',
        name: 'Fill',
        icon: '✏️',
        description: 'Fill an input field'
      },
      {
        type: 'type',
        name: 'Type',
        icon: '⌨️',
        description: 'Type text into an element'
      },
      {
        type: 'selectOption',
        name: 'Select Option',
        icon: '📋',
        description: 'Select option from dropdown'
      },
      {
        type: 'check',
        name: 'Check',
        icon: '☑️',
        description: 'Check a checkbox'
      },
      {
        type: 'uncheck',
        name: 'Uncheck',
        icon: '☐',
        description: 'Uncheck a checkbox'
      },
      {
        type: 'conditionalClick',
        name: 'Conditional Click',
        icon: '🔀',
        description: 'Click element based on condition (exists/enabled/disabled)'
      }
    ]
  },
  {
    name: 'Waiting',
    icon: '⏳',
    steps: [
      {
        type: 'waitForSelector',
        name: 'Wait for Element',
        icon: '👁️',
        description: 'Wait for an element to appear'
      },
      {
        type: 'waitForTimeout',
        name: 'Wait for Time',
        icon: '⏰',
        description: 'Wait for a specific duration'
      }
    ]
  },
  {
    name: 'Credentials',
    icon: '🔐',
    steps: [
      {
        type: 'fillPassword',
        name: 'Fill Password',
        icon: '🔑',
        description: 'Fill password from stored credentials'
      },
      {
        type: 'fill2FA',
        name: 'Fill 2FA',
        icon: '🔐',
        description: 'Fill 2FA code from stored credentials'
      }
    ]
  },
  {
    name: 'Data Extraction',
    icon: '📊',
    steps: [
      {
        type: 'extractText',
        name: 'Extract Text',
        icon: '📝',
        description: 'Extract text from an element'
      },
      {
        type: 'takeScreenshot',
        name: 'Screenshot',
        icon: '📸',
        description: 'Take a screenshot'
      },
      {
        type: 'downloadFile',
        name: 'Download File',
        icon: '📥',
        description: 'Download a file and optionally convert to base64'
      }
    ]
  },
  {
    name: 'AI Processing',
    icon: '🤖',
    steps: [
      {
        type: 'extractPdfText',
        name: 'Extract PDF Text',
        icon: '📄',
        description: 'Extract text from PDF and process with AI'
      }
    ]
  }
]

export function StepToolbar({ onAddStep }: StepToolbarProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set() // All categories collapsed by default
  )
  const { containerRef, overlayRef, fadeOverlayStyle } = useScrollFade()

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryName)) {
        newSet.delete(categoryName)
      } else {
        newSet.add(categoryName)
      }
      return newSet
    })
  }

  return (
    <div style={{
      height: '100%',
      padding: '2rem 2rem 2rem 0.5rem',
      display: 'flex',
      flexDirection: 'column',
      gap: '0.75rem',
      backgroundColor: 'transparent'
    }}>
      {/* Categories - Scrollable Area */}
      <div style={{ position: 'relative', flex: 1 }}>
        <div
          ref={containerRef}
          style={{
            height: '100%',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem',
            paddingRight: '0.25rem',
            marginRight: '-0.25rem'
          }}
          className="custom-scrollbar"
        >
        {stepCategories.map((category) => {
          const isExpanded = expandedCategories.has(category.name)

          return (
            <div
              key={category.name}
              style={{
                backgroundColor: '#ffffff',
                borderRadius: '1rem',
                border: '1px solid #cbd5e1',
                marginBottom: '0.25rem',
                overflow: 'hidden',
                transition: 'all 0.3s ease-in-out'
              }}
            >
              {/* Category Header - Clickable */}
              <button
                onClick={() => toggleCategory(category.name)}
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.375rem',
                  padding: '0.75rem 0.5rem',
                  backgroundColor: 'transparent',
                  border: 'none',
                  fontSize: '0.625rem',
                  fontWeight: '700',
                  color: '#475569',
                  textTransform: 'uppercase',
                  letterSpacing: '0.025em',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <span style={{ fontSize: '0.875rem' }}>{category.icon}</span>
                {category.name}
                <span style={{
                  marginLeft: 'auto',
                  fontSize: '0.75rem',
                  transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s'
                }}>
                  ▶
                </span>
              </button>

              {/* Category Steps - Inside the container with transition */}
              <div
                style={{
                  maxHeight: isExpanded ? `${category.steps.length * 3.5}rem` : '0',
                  overflow: 'hidden',
                  transition: 'max-height 0.3s ease-in-out'
                }}
              >
                <div style={{
                  padding: '0.5rem 0.5rem 0.5rem 0.5rem',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.25rem',
                  opacity: isExpanded ? 1 : 0,
                  transition: 'opacity 0.2s ease-in-out'
                }}>
                  {category.steps.map((step) => (
                    <button
                      key={step.type}
                      onClick={() => onAddStep(step.type)}
                      style={{
                        width: '100%',
                        justifyContent: 'flex-start',
                        gap: '0.5rem',
                        textAlign: 'left',
                        height: 'auto',
                        padding: '0.5rem 0.75rem',
                        minHeight: '2rem',
                        backgroundColor: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        color: '#374151'
                      }}
                      title={step.description}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e2e8f0'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8fafc'
                      }}
                    >
                      <span style={{ fontSize: '1rem', flexShrink: 0 }}>{step.icon}</span>
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div style={{
                          fontSize: '0.75rem',
                          fontWeight: '500',
                          lineHeight: '1.2',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {step.name}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )
        })}
        </div>
        <div ref={overlayRef} style={fadeOverlayStyle} />
      </div>
    </div>
  )
}

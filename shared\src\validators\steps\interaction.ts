import { RpaStep, FillStep, TypeStep, ValidationResult, ValidationError, createStepFromType } from '../../types';

export function validateInteractionStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Most interaction steps require a selector
  if (['click', 'fill', 'type', 'selectOption', 'check', 'uncheck'].includes(step.type)) {
    const stepWithSelector = step as any;
    if (!stepWithSelector.selector || stepWithSelector.selector.trim() === '') {
      errors.push({
        field: 'selector',
        message: 'Selector is required',
        code: 'REQUIRED'
      });
    }
  }

  // Type-specific validation
  switch (step.type) {
    case 'fill':
      const fillStep = step as FillStep;
      if (fillStep.value === undefined || fillStep.value === null) {
        errors.push({
          field: 'value',
          message: 'Value is required for fill step',
          code: 'REQUIRED'
        });
      }
      break;

    case 'type':
      const typeStep = step as TypeStep;
      if (!typeStep.text || typeStep.text.trim() === '') {
        errors.push({
          field: 'text',
          message: 'Text is required for type step',
          code: 'REQUIRED'
        });
      }
      break;

    case 'click':
    case 'selectOption':
    case 'check':
    case 'uncheck':
      // These have basic selector validation which is already covered above
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createInteractionStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'click':
    case 'fill':
    case 'type':
    case 'selectOption':
    case 'check':
    case 'uncheck':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an interaction step type: ${stepType}`);
  }
}

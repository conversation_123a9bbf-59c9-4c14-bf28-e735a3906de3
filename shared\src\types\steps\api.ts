import { RpaStepBase } from './base';

// API steps (future implementation)
// These are placeholder types for future API integration functionality

export interface ApiCallStep extends RpaStepBase {
  type: 'apiCall';
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  variableName?: string; // Variable name to store response
}

export interface ApiAuthStep extends RpaStepBase {
  type: 'apiAuth';
  authType: 'bearer' | 'basic' | 'apikey';
  credentialId: string;
  variableName?: string; // Variable name to store auth token
}

import { RpaStep, RpaFlow, ValidationResult, ValidationError } from './types';

export function validateStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate common fields
  if (!step.id || step.id.trim() === '') {
    errors.push({
      field: 'id',
      message: 'Step ID is required',
      code: 'REQUIRED'
    });
  }

  if (!step.type || step.type.trim() === '') {
    errors.push({
      field: 'type',
      message: 'Step type is required',
      code: 'REQUIRED'
    });
  }

  if (step.timeout && (step.timeout < 0 || step.timeout > 300000)) {
    errors.push({
      field: 'timeout',
      message: 'Timeout must be between 0 and 300000ms',
      code: 'INVALID_RANGE'
    });
  }

  // Validate type-specific fields
  switch (step.type) {
    case 'navigate':
      if (!step.url || step.url.trim() === '') {
        errors.push({
          field: 'url',
          message: 'URL is required for navigate step',
          code: 'REQUIRED'
        });
      } else {
        try {
          new URL(step.url);
        } catch {
          errors.push({
            field: 'url',
            message: 'Invalid URL format',
            code: 'INVALID_FORMAT'
          });
        }
      }
      break;

    case 'click':
    case 'fill':
    case 'type':
    case 'waitForSelector':
    case 'extractText':
    case 'extractAttribute':
      if (!step.selector || step.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          code: 'REQUIRED'
        });
      }
      break;

    case 'fill':
      if (step.value === undefined || step.value === null) {
        errors.push({
          field: 'value',
          message: 'Value is required for fill step',
          code: 'REQUIRED'
        });
      }
      break;

    case 'type':
      if (!step.text || step.text.trim() === '') {
        errors.push({
          field: 'text',
          message: 'Text is required for type step',
          code: 'REQUIRED'
        });
      }
      break;

    case 'waitForTimeout':
      if (!step.duration || step.duration <= 0) {
        errors.push({
          field: 'duration',
          message: 'Duration must be greater than 0',
          code: 'INVALID_VALUE'
        });
      }
      break;

    case 'extractText':
    case 'extractAttribute':
      if (!step.variableName || step.variableName.trim() === '') {
        errors.push({
          field: 'variableName',
          message: 'Variable name is required',
          code: 'REQUIRED'
        });
      }
      break;

    case 'extractPdfText':
      if (!step.base64Input || step.base64Input.trim() === '') {
        errors.push({
          field: 'base64Input',
          message: 'Base64 input is required for PDF text extraction',
          code: 'REQUIRED'
        });
      }
      if (!step.prompt || step.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for PDF text extraction',
          code: 'REQUIRED'
        });
      }
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function validateFlow(flow: RpaFlow): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate flow properties
  if (!flow.id || flow.id.trim() === '') {
    errors.push({
      field: 'id',
      message: 'Flow ID is required',
      code: 'REQUIRED'
    });
  }

  if (!flow.name || flow.name.trim() === '') {
    errors.push({
      field: 'name',
      message: 'Flow name is required',
      code: 'REQUIRED'
    });
  }

  if (!flow.customerId || flow.customerId.trim() === '') {
    errors.push({
      field: 'customerId',
      message: 'Customer ID is required',
      code: 'REQUIRED'
    });
  }

  // Note: We allow flows without steps to be saved (empty flows)
  // Steps validation is only done when the flow actually has steps

  // Validate each step
  if (flow.steps) {
    flow.steps.forEach((step, index) => {
      const stepValidation = validateStep(step);
      if (!stepValidation.valid) {
        stepValidation.errors.forEach(error => {
          errors.push({
            field: `steps[${index}].${error.field}`,
            message: `Step ${index + 1}: ${error.message}`,
            code: error.code
          });
        });
      }
    });
  }

  // Validate settings
  if (flow.settings) {
    if (flow.settings.viewport) {
      if (flow.settings.viewport.width <= 0 || flow.settings.viewport.height <= 0) {
        errors.push({
          field: 'settings.viewport',
          message: 'Viewport dimensions must be positive',
          code: 'INVALID_VALUE'
        });
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function validateStepType(type: string): boolean {
  const validTypes = [
    'navigate', 'goBack', 'goForward', 'reload',
    'click', 'fill', 'type', 'selectOption', 'check', 'uncheck',
    'waitForSelector', 'waitForTimeout', 'waitForUrl',
    'extractText', 'extractAttribute', 'takeScreenshot',
    'ifElementExists', 'conditionalClick',
    'fillPassword', 'fill2FA', 'downloadFile', 'extractPdfText'
  ];

  return validTypes.includes(type);
}

export function validateSelector(selector: string): ValidationResult {
  const errors: ValidationError[] = [];

  if (!selector || selector.trim() === '') {
    errors.push({
      field: 'selector',
      message: 'Selector cannot be empty',
      code: 'REQUIRED'
    });
    return { valid: false, errors };
  }

  // Basic CSS selector validation
  try {
    // This is a simple check - in a real browser environment
    // we could use document.querySelector to validate
    if (selector.includes('>>') || selector.includes('xpath=')) {
      // Playwright-specific selectors are valid
      return { valid: true, errors: [] };
    }
    
    // Basic CSS selector patterns
    const cssPattern = /^[a-zA-Z0-9\s\[\]="'.:>#_-]+$/;
    if (!cssPattern.test(selector)) {
      errors.push({
        field: 'selector',
        message: 'Invalid selector format',
        code: 'INVALID_FORMAT'
      });
    }
  } catch {
    errors.push({
      field: 'selector',
      message: 'Invalid selector syntax',
      code: 'INVALID_SYNTAX'
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

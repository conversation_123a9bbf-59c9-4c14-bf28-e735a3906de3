import { useState } from 'react'
import { validateStep, NavigateStep, GoBackStep, GoForwardStep, ReloadStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { VariableField, SelectField } from '../base/FieldComponents'

type NavigationStep = NavigateStep | GoBackStep | GoForwardStep | ReloadStep

interface NavigationStepEditorProps extends BaseStepEditorProps {
  step: NavigationStep
}

export function NavigationStepEditor({ 
  step, 
  onSave, 
  onCancel, 
  compact = false, 
  steps = [], 
  currentStepIndex = 0 
}: NavigationStepEditorProps) {
  const [editedStep, setEditedStep] = useState<NavigationStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<NavigationStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as NavigationStep))
  }

  const renderNavigationFields = () => {
    switch (editedStep.type) {
      case 'navigate':
        const navigateStep = editedStep as NavigateStep
        return (
          <>
            <VariableField
              label="URL"
              value={navigateStep.url || ''}
              onChange={(value) => updateStep({ url: value })}
              placeholder="https://example.com"
              type="url"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
              fullWidth={true}
            />
            <SelectField
              label="Vänta tills"
              value={navigateStep.waitUntil || 'load'}
              onChange={(value) => updateStep({ waitUntil: value as any })}
              options={[
                { value: 'load', label: 'Sidan laddad' },
                { value: 'domcontentloaded', label: 'DOM innehåll laddat' },
                { value: 'networkidle', label: 'Nätverk inaktivt' }
              ]}
              compact={compact}
            />
          </>
        )

      case 'goBack':
      case 'goForward':
      case 'reload':
        // These steps don't have specific configuration
        return (
          <div style={{ 
            padding: '1rem', 
            backgroundColor: '#f9fafb', 
            borderRadius: '0.5rem',
            color: '#6b7280',
            fontSize: compact ? '0.75rem' : '0.875rem'
          }}>
            Detta steg kräver ingen specifik konfiguration.
          </div>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      navigate: 'Navigate',
      goBack: 'Go Back',
      goForward: 'Go Forward',
      reload: 'Reload'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderNavigationFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}

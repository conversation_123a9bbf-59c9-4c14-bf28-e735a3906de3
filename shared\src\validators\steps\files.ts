import { RpaStep, DownloadFileStep, ValidationResult, ValidationError, createStepFromType } from '../../types';

export function validateFileStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep;
      
      // triggerSelector is optional, but if provided should not be empty
      if (downloadStep.triggerSelector !== undefined && downloadStep.triggerSelector.trim() === '') {
        errors.push({
          field: 'triggerSelector',
          message: 'Trigger selector cannot be empty if provided',
          code: 'INVALID_VALUE'
        });
      }

      // filename is optional
      // variableName is optional
      // saveToFile is optional (defaults to false)
      // forceDownload is optional (defaults to false)
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createFileStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'downloadFile':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a file step type: ${stepType}`);
  }
}

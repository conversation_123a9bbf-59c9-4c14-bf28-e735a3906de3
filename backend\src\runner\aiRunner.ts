import OpenAI from 'openai';
import pdfParse from 'pdf-parse';
import {
  RpaFlow,
  RpaStep,
  FlowSettings,
  ExecutionLog,
  interpolateVariables,
  RpaStepBase
} from '@rpa-project/shared';
import { BaseRunner, RunnerContext, StepExecutionResult } from './IRunner';
import { AI_PROCESSING_STEPS } from './stepTypes';

// Local type definition until shared package is updated
interface ExtractPdfTextStep extends RpaStepBase {
  type: 'extractPdfText';
  base64Input: string; // Base64 PDF data or variable reference
  prompt: string; // User prompt for LLM processing
  variableName?: string; // Variable name to store AI response (default: 'var-ai-response')
}

/**
 * <PERSON><PERSON>un<PERSON> handles AI-based processing steps like PDF text extraction and LLM processing
 */
export class AIRunner extends BaseRunner {
  private openai: OpenAI;

  constructor() {
    super();
    
    // Initialize OpenAI client
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    // AI runner doesn't need special initialization
    // Variables are handled in the context
  }

  getSupportedStepTypes(): string[] {
    return [...AI_PROCESSING_STEPS];
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    const { variables, onLog } = context;

    try {
      // Type assertion to handle the new step type until shared package is updated
      const stepType = (step as any).type;

      switch (stepType) {
        case 'extractPdfText':
          return await this.executeExtractPdfText(step as unknown as ExtractPdfTextStep, context);

        default:
          return {
            success: false,
            error: `Unsupported step type: ${stepType}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const stepType = (step as any).type;
      onLog({
        level: 'error',
        message: `Error executing step ${stepType}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  private async executeExtractPdfText(
    step: ExtractPdfTextStep, 
    context: RunnerContext
  ): Promise<StepExecutionResult> {
    const { variables, onLog } = context;

    try {
      // Interpolate base64Input to handle variable references
      const interpolatedBase64Input = interpolateVariables(step.base64Input, variables);
      const interpolatedPrompt = interpolateVariables(step.prompt, variables);

      onLog({
        level: 'info',
        message: 'Starting PDF text extraction...',
        stepId: step.id
      });

      // Extract base64 data (remove data URL prefix if present)
      let base64Data = interpolatedBase64Input;
      if (base64Data.includes(',')) {
        base64Data = base64Data.split(',')[1];
      }

      // Convert base64 to buffer
      const pdfBuffer = Buffer.from(base64Data, 'base64');

      onLog({
        level: 'info',
        message: `PDF buffer created, size: ${pdfBuffer.length} bytes`,
        stepId: step.id
      });

      // Extract text from PDF
      const pdfData = await pdfParse(pdfBuffer);
      const extractedText = pdfData.text;

      onLog({
        level: 'info',
        message: `Extracted ${extractedText.length} characters from PDF`,
        stepId: step.id
      });

      // Check if OpenAI API key is configured
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }

      onLog({
        level: 'info',
        message: 'Sending text to OpenAI for processing...',
        stepId: step.id
      });

      // Send to OpenAI for processing
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'Du är en AI-assistent som hjälper till att extrahera och bearbeta information från dokument. Svara på svenska och var koncis och tydlig.'
          },
          {
            role: 'user',
            content: `Här är texten från ett PDF-dokument:\n\n${extractedText}\n\nAnvändarens instruktion: ${interpolatedPrompt}\n\nBearbeta texten enligt instruktionen och ge ett tydligt svar.`
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from OpenAI');
      }

      // Store result in variable
      const variableName = step.variableName || 'var-ai-response';
      variables[variableName] = aiResponse;
      context.variables[variableName] = aiResponse;

      onLog({
        level: 'info',
        message: `PDF text extraction and AI processing completed. Result stored in variable: ${variableName}`,
        stepId: step.id,
        data: { [variableName]: aiResponse.substring(0, 200) + (aiResponse.length > 200 ? '...' : '') }
      });

      return {
        success: true,
        data: { [variableName]: aiResponse }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Failed to extract PDF text: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    // AI runner doesn't need cleanup
  }
}

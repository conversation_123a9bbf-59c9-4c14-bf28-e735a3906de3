import { RpaStep, IfElementExistsStep, ConditionalClickStep, ValidationResult, ValidationError, createStepFromType } from '../../types';

export function validateConditionalStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'ifElementExists':
      const ifStep = step as IfElementExistsStep;
      
      if (!ifStep.selector || ifStep.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          code: 'REQUIRED'
        });
      }

      // Note: thenSteps and elseSteps validation would require recursive step validation
      // This could be implemented later if needed
      break;

    case 'conditionalClick':
      const condClickStep = step as ConditionalClickStep;
      
      if (!condClickStep.selector || condClickStep.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          code: 'REQUIRED'
        });
      }

      if (!condClickStep.condition || !['exists', 'enabled', 'disabled'].includes(condClickStep.condition)) {
        errors.push({
          field: 'condition',
          message: 'Condition must be one of: exists, enabled, disabled',
          code: 'INVALID_VALUE'
        });
      }
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createConditionalStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'ifElementExists':
    case 'conditionalClick':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a conditional step type: ${stepType}`);
  }
}

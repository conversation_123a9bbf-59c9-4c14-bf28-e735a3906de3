import { RpaStep, ExtractTextStep, ExtractAttributeStep, ValidationResult, ValidationError, createStepFromType } from '../../types';

export function validateExtractionStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractText':
    case 'extractAttribute':
      // Both require selector and variableName
      if (!step.selector || step.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          code: 'REQUIRED'
        });
      }

      const extractStep = step as ExtractTextStep | ExtractAttributeStep;
      if (!extractStep.variableName || extractStep.variableName.trim() === '') {
        errors.push({
          field: 'variableName',
          message: 'Variable name is required',
          code: 'REQUIRED'
        });
      }

      // Additional validation for extractAttribute
      if (step.type === 'extractAttribute') {
        const attrStep = step as ExtractAttributeStep;
        if (!attrStep.attribute || attrStep.attribute.trim() === '') {
          errors.push({
            field: 'attribute',
            message: 'Attribute name is required',
            code: 'REQUIRED'
          });
        }
      }
      break;

    case 'takeScreenshot':
      // Screenshot step doesn't require additional validation
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function createExtractionStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractText':
    case 'extractAttribute':
    case 'takeScreenshot':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an extraction step type: ${stepType}`);
  }
}

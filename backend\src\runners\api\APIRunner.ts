import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';
import { BaseRunner, Runner<PERSON>ontext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';

/**
 * APIRunner for handling API-based steps (future implementation)
 * This is a placeholder for future API integration capabilities
 */
export class APIRunner extends BaseRunner {

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    // API runner initialization will be implemented in the future
    this.log('info', 'APIRunner initialized (placeholder implementation)');
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'api'
    );
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    const { onLog } = context;

    onLog({
      level: 'warn',
      message: `APIRunner is not yet implemented. Step type '${step.type}' will be skipped.`,
      stepId: step.id
    });

    return {
      success: false,
      error: `APIRunner is not yet implemented for step type: ${step.type}`
    };
  }

  async cleanup(): Promise<void> {
    // API runner cleanup will be implemented in the future
  }
}

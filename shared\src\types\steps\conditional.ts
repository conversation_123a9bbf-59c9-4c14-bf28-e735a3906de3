import { RpaStepBase } from './base';

// Forward declaration for RpaStep - this creates a circular dependency
// that will be resolved when the full type system is assembled
export interface IfElementExistsStep extends RpaStepBase {
  type: 'ifElementExists';
  selector: string;
  thenSteps: RpaStepBase[]; // Using base type to avoid circular dependency
  elseSteps?: RpaStepBase[]; // Using base type to avoid circular dependency
}

export interface ConditionalClickStep extends RpaStepBase {
  type: 'conditionalClick';
  selector: string;
  condition: 'exists' | 'enabled' | 'disabled';
  clickIfTrue?: boolean; // default true
  button?: 'left' | 'right' | 'middle';
  force?: boolean;
}

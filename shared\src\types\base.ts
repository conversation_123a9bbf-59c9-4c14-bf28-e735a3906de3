// Base types for RPA automation

export interface RpaStepBase {
  id: string;
  type: string;
  description?: string;
  timeout?: number; // milliseconds, default 30000
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Utility function for generating IDs
export function generateId(): string {
  return Math.random().toString(36).substring(2, 11) + Date.now().toString(36)
}

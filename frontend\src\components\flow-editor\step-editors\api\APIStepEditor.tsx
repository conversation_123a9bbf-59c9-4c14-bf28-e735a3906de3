import { useState } from 'react'
import { RpaStep, validateStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'

// Placeholder for future API step types
type APIStep = RpaStep

interface APIStepEditorProps extends BaseStepEditorProps {
  step: APIStep
}

export function APIStepEditor({
  step,
  onSave,
  onCancel,
  compact = false
}: APIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<APIStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<APIStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as APIStep))
  }

  const renderAPIFields = () => {
    // Placeholder for future API step implementations
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        backgroundColor: '#f9fafb', 
        borderRadius: '0.5rem',
        color: '#6b7280',
        fontSize: compact ? '0.875rem' : '1rem'
      }}>
        <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>🚧</div>
        <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>API Steps Coming Soon</div>
        <div style={{ fontSize: '0.875rem' }}>
          API step editors will be implemented in future versions.
        </div>
      </div>
    )
  }

  const getTitle = () => {
    return compact ? `Konfigurera API-steg` : `Edit API Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAPIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
